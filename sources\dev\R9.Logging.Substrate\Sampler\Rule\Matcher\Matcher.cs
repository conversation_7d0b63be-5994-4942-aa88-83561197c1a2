// <copyright file="Matcher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Matcher class is used to match log entries against a set of constraints.
    /// There are partial definitions of the class to separate the logic for different constraint types.
    /// </summary>
    internal partial class Matcher
    {
        private delegate bool MatchFunc(LogLevel logLevel, EventId eventId, IReadOnlyList<KeyValuePair<string, object?>> state);

#if DEBUG
        private readonly List<KeyValuePair<MatchFunc, string>> matchFuncList;
#else
        private readonly List<MatchFunc> matchFuncList;
#endif

        /// <summary>
        /// Initializes a new instance of the <see cref="Matcher"/> class with a list of constraints.
        /// </summary>
        /// <param name="constraints">The constraints used to match log entries.</param>
        public Matcher(List<Constraint> constraints)
        {
#if DEBUG
            matchFuncList = new List<KeyValuePair<MatchFunc, string>>();
            if (constraints.Count == 0)
            {
                // If there are no constraints, add an always-true match function
                matchFuncList.Add(new KeyValuePair<MatchFunc, string>(GenerateAlwaysTrueMatchFunc(), "No constraints - always match"));
                return;
            }
            foreach (var constraint in constraints)
            {
                var func = GenerateMatchFunc(constraint);
                var debugInfo = GenerateDebugInfo(constraint);
                matchFuncList.Add(new KeyValuePair<MatchFunc, string>(func, debugInfo));
            }
#else
            matchFuncList = new List<MatchFunc>();
            if (constraints.Count == 0)
            {
                // If there are no constraints, add an always-true match function
                matchFuncList.Add(GenerateAlwaysTrueMatchFunc());
                return;
            }
            foreach (var constraint in constraints)
            {
                var func = GenerateMatchFunc(constraint);
                matchFuncList.Add(func);
            }
#endif
        }

        /// <summary>
        /// Tries to match the given log entry against the constraints defined in the matcher.
        /// </summary>
        /// <typeparam name="TState">TState</typeparam>
        /// <param name="logEntry"></param>
        /// <returns>True if the log entry matches all constraints of the rule; otherwise false.</returns>
        public bool TryMatch<TState>(in LogEntry<TState> logEntry)
        {
            IReadOnlyList<KeyValuePair<string, object?>> state;
            if (logEntry.State is IReadOnlyList<KeyValuePair<string, object?>> stateList)
            {
                state = stateList;
            }
            else
            {
                // Use empty state for null reference or unexpected data structure to avoid blocking those match functions that don't depend on state
                state = Array.Empty<KeyValuePair<string, object?>>();
            }

#if DEBUG
            foreach (var matchFuncPair in matchFuncList)
            {
                var func = matchFuncPair.Key; // Debug info is available in matchFuncPair.Value if needed
                if (!func(logEntry.LogLevel, logEntry.EventId, state))
                {
                    return false;
                }
            }
#else
            foreach (var func in matchFuncList)
            {
                if (!func(logEntry.LogLevel, logEntry.EventId, state))
                {
                    return false;
                }
            }
#endif
            return true;
        }

        private static string GenerateDebugInfo(Constraint constraint)
        {
            var type = constraint.Type;
            var ruleOperator = constraint.RuleOperator;
            var key = constraint.Field;
            var value = constraint.Value;
            return $"{type} {key} {ruleOperator} {value}";
        }

        private static MatchFunc GenerateAlwaysTrueMatchFunc()
        {
            return (logLevel, eventId, state) =>
            {
                return true;
            };
        }

        private static MatchFunc GenerateMatchFunc(Constraint constraint) =>
            constraint.Type switch
            {
                ConstraintType.String => GenerateStringMatchFunc(constraint),
                ConstraintType.Long => GenerateLongMatchFunc(constraint),
                ConstraintType.Double => GenerateDoubleMatchFunc(constraint),
                ConstraintType.EventId => GenerateEventIdMatchFunc(constraint),
                ConstraintType.LogLevel => GenerateLogLevelMatchFunc(constraint),
                ConstraintType.Enum => GenerateEnumMatchFunc(constraint),
                _ => GenerateAlwaysTrueMatchFunc()
            };
    }
}

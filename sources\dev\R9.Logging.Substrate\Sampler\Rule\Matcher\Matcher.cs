// <copyright file="Matcher.cs" company="Microsoft">
//     Copyright (c) Microsoft Corporation.  All rights reserved.
// </copyright>

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;

namespace Microsoft.M365.Core.Telemetry.R9.Logging.Substrate.Sampler
{
    /// <summary>
    /// Matcher class is used to match log entries against a set of constraints.
    /// There are partial definitions of the class to separate the logic for different constraint types.
    /// </summary>
    internal partial class Matcher
    {
        private delegate bool MatchMethod(LogLevel logLevel, EventId eventId, IReadOnlyList<KeyValuePair<string, object?>> state);

#if DEBUG
        private readonly KeyValuePair<MatchMethod, string>[] matchMethods;
#else
        private readonly MatchMethod[] matchMethods;
#endif

        /// <summary>
        /// Initializes a new instance of the <see cref="Matcher"/> class with a list of constraints.
        /// </summary>
        /// <param name="constraints">The constraints used to match log entries.</param>
        public Matcher(List<Constraint> constraints)
        {
#if DEBUG
            if (constraints.Count == 0)
            {
                // If there are no constraints, add an always-true match function
                matchMethods = new KeyValuePair<MatchMethod, string>[]
                {
                    new KeyValuePair<MatchMethod, string>(GenerateAlwaysTrueMatchMethod(), "No constraints - always match")
                };
                return;
            }

            matchMethods = new KeyValuePair<MatchMethod, string>[constraints.Count];
            for (int i = 0; i < constraints.Count; i++)
            {
                var constraint = constraints[i];
                var func = GenerateMatchMethod(constraint);
                var debugInfo = GenerateDebugInfo(constraint);
                matchMethods[i] = new KeyValuePair<MatchMethod, string>(func, debugInfo);
            }
#else
            if (constraints.Count == 0)
            {
                // If there are no constraints, add an always-true match function
                matchMethods = new MatchMethod[] { GenerateAlwaysTrueMatchMethod() };
                return;
            }

            matchMethods = new MatchMethod[constraints.Count];
            for (int i = 0; i < constraints.Count; i++)
            {
                var constraint = constraints[i];
                var func = GenerateMatchMethod(constraint);
                matchMethods[i] = func;
            }
#endif
        }

        /// <summary>
        /// Tries to match the given log entry against the constraints defined in the matcher.
        /// </summary>
        /// <typeparam name="TState">TState</typeparam>
        /// <param name="logEntry"></param>
        /// <returns>True if the log entry matches all constraints of the rule; otherwise false.</returns>
        public bool TryMatch<TState>(in LogEntry<TState> logEntry)
        {
            IReadOnlyList<KeyValuePair<string, object?>> state;
            if (logEntry.State is IReadOnlyList<KeyValuePair<string, object?>> stateList)
            {
                state = stateList;
            }
            else
            {
                // Use empty state for null reference or unexpected data structure to avoid blocking those match functions that don't depend on state
                state = Array.Empty<KeyValuePair<string, object?>>();
            }

#if DEBUG
            foreach (var matchMethodPair in matchMethods)
            {
                var func = matchMethodPair.Key; // Debug info is available in matchMethodPair.Value if needed
                if (!func(logEntry.LogLevel, logEntry.EventId, state))
                {
                    return false;
                }
            }
#else
            foreach (var func in matchMethods)
            {
                if (!func(logEntry.LogLevel, logEntry.EventId, state))
                {
                    return false;
                }
            }
#endif
            return true;
        }

        private static string GenerateDebugInfo(Constraint constraint)
        {
            var type = constraint.Type;
            var ruleOperator = constraint.RuleOperator;
            var key = constraint.Field;
            var value = constraint.Value;
            return $"{type} {key} {ruleOperator} {value}";
        }

        private static MatchMethod GenerateAlwaysTrueMatchMethod()
        {
            return (logLevel, eventId, state) =>
            {
                return true;
            };
        }

        private static MatchMethod GenerateMatchMethod(Constraint constraint) =>
            constraint.Type switch
            {
                ConstraintType.String => GenerateStringMatchMethod(constraint),
                ConstraintType.Long => GenerateLongMatchMethod(constraint),
                ConstraintType.Double => GenerateDoubleMatchMethod(constraint),
                ConstraintType.EventId => GenerateEventIdMatchMethod(constraint),
                ConstraintType.LogLevel => GenerateLogLevelMatchMethod(constraint),
                ConstraintType.Enum => GenerateEnumMatchMethod(constraint),
                _ => GenerateAlwaysTrueMatchMethod()
            };
    }
}
